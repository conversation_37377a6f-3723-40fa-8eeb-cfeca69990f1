/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{"path":"src\\pages\\_app.tsx","import":"Cairo","arguments":[{"subsets":["arabic","latin"],"weight":["300","400","500","600","700","800","900"],"variable":"--font-cairo","display":"swap"}],"variableName":"cairo"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Cairo_baae29';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}@font-face {font-family: '__Cairo_Fallback_baae29';src: local(\\\"Arial\\\");ascent-override: 136.94%;descent-override: 60.01%;line-gap-override: 0.00%;size-adjust: 95.15%\\n}.__className_baae29 {font-family: '__Cairo_baae29', '__Cairo_Fallback_baae29';font-style: normal\\n}.__variable_baae29 {--font-cairo: '__Cairo_baae29', '__Cairo_Fallback_baae29'\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://../../node_modules/next/font/google/%3Cinput%20css%203_DzAw%3E\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,cAAc;AACd;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,sEAAsE;EACtE,iNAAiN;AACnN;AACA,UAAU;AACV;EACE,6BAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L,CC5LA,YAAA,uCAAA,oBAAA,yBAAA,yBAAA,yBAAA,mBAAA;CAAA,qBAAA,yDAAA,kBAAA;CAAA,oBAAA,yDAAA;CAAA\",\"sourcesContent\":[\"/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 600;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/2dc625304a276794-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin-ext */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/5ec84f17416dda4d-s.woff2) format('woff2');\\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Cairo';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/01f0c602c274ea55-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n\",null],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"style\": {\"fontFamily\":\"'__Cairo_baae29', '__Cairo_Fallback_baae29'\",\"fontStyle\":\"normal\"},\n\t\"className\": \"__className_baae29\",\n\t\"variable\": \"__variable_baae29\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}\n"));

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{"path":"src\\pages\\_app.tsx","import":"Tajawal","arguments":[{"subsets":["arabic","latin"],"weight":["300","400","500","700","800","900"],"variable":"--font-tajawal","display":"swap"}],"variableName":"tajawal"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/a7017400c9fd40b6-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/149bd79232cbc8b2-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/dd994fbf464986f0-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/e97026df054cf2a3-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/63a79a6cf340c5d2-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/f15f45d13243c643-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/1ebb550cd0a67fc6-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/ce401babc0566bc1-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/6d87047c78b383ca-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/b9817c66466e8cbc-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/420b3da95f5fbf98-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: '__Tajawal_a9af04';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/0a61324d85234ed0-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}@font-face {font-family: '__Tajawal_Fallback_a9af04';src: local(\\\"Arial\\\");ascent-override: 67.42%;descent-override: 37.43%;line-gap-override: 20.97%;size-adjust: 95.38%\\n}.__className_a9af04 {font-family: '__Tajawal_a9af04', '__Tajawal_Fallback_a9af04';font-style: normal\\n}.__variable_a9af04 {--font-tajawal: '__Tajawal_a9af04', '__Tajawal_Fallback_a9af04'\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://../../node_modules/next/font/google/%3Cinput%20css%20ERJia-%3E\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L;AACA,WAAW;AACX;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,soBAAsoB;AACxoB;AACA,UAAU;AACV;EACE,+BAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,wEAAwE;EACxE,yLAAyL;AAC3L,CC3GA,YAAA,yCAAA,oBAAA,wBAAA,yBAAA,0BAAA,mBAAA;CAAA,qBAAA,6DAAA,kBAAA;CAAA,oBAAA,+DAAA;CAAA\",\"sourcesContent\":[\"/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/a7017400c9fd40b6-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 300;\\n  font-display: swap;\\n  src: url(/_next/static/media/149bd79232cbc8b2-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/dd994fbf464986f0-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 400;\\n  font-display: swap;\\n  src: url(/_next/static/media/e97026df054cf2a3-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/63a79a6cf340c5d2-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 500;\\n  font-display: swap;\\n  src: url(/_next/static/media/f15f45d13243c643-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/1ebb550cd0a67fc6-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 700;\\n  font-display: swap;\\n  src: url(/_next/static/media/ce401babc0566bc1-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/6d87047c78b383ca-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 800;\\n  font-display: swap;\\n  src: url(/_next/static/media/b9817c66466e8cbc-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n/* arabic */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/420b3da95f5fbf98-s.p.woff2) format('woff2');\\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\\n}\\n/* latin */\\n@font-face {\\n  font-family: 'Tajawal';\\n  font-style: normal;\\n  font-weight: 900;\\n  font-display: swap;\\n  src: url(/_next/static/media/0a61324d85234ed0-s.p.woff2) format('woff2');\\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n}\\n\",null],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"style\": {\"fontFamily\":\"'__Tajawal_a9af04', '__Tajawal_Fallback_a9af04'\",\"fontStyle\":\"normal\"},\n\t\"className\": \"__className_a9af04\",\n\t\"variable\": \"__variable_a9af04\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}\n"));

/***/ }),

/***/ "../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/font/google/target.css?{"path":"src\\pages\\_app.tsx","import":"Cairo","arguments":[{"subsets":["arabic","latin"],"weight":["300","400","500","600","700","800","900"],"variable":"--font-cairo","display":"swap"}],"variableName":"cairo"} ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-cairo\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-cairo\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\",\n      function () {\n        content = __webpack_require__(/*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-cairo\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}\n"));

/***/ }),

/***/ "../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/font/google/target.css?{"path":"src\\pages\\_app.tsx","import":"Tajawal","arguments":[{"subsets":["arabic","latin"],"weight":["300","400","500","700","800","900"],"variable":"--font-tajawal","display":"swap"}],"variableName":"tajawal"} ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\",\n      function () {\n        content = __webpack_require__(/*! !!../../dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!./target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[3].use[1]!../../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[7].oneOf[3].use[2]!../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}\n"));

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Noto_Sans_Arabic\",\"arguments\":[{\"subsets\":[\"arabic\"],\"variable\":\"--font-arabic\",\"display\":\"swap\"}],\"variableName\":\"notoSansArabic\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Arabic\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\"],\\\"variable\\\":\\\"--font-arabic\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansArabic\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-display\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-display\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-cairo\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _utils_errorFilter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/errorFilter */ \"./src/utils/errorFilter.ts\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_8__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // Set document direction based on locale\n        document.documentElement.dir = isRTL ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = locale || \"ar\";\n        // Initialize error filtering for extension errors\n        (0,_utils_errorFilter__WEBPACK_IMPORTED_MODULE_7__.initializeErrorFilter)();\n    }, [\n        locale,\n        isRTL\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default().variable), \" \").concat((next_font_google_target_css_path_src_pages_app_tsx_import_Noto_Sans_Arabic_arguments_subsets_arabic_variable_font_arabic_display_swap_variableName_notoSansArabic___WEBPACK_IMPORTED_MODULE_10___default().variable), \" \").concat((next_font_google_target_css_path_src_pages_app_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_display_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_11___default().variable), \" \").concat((next_font_google_target_css_path_src_pages_app_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_500_600_700_800_900_variable_font_cairo_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_12___default().variable), \" \").concat((next_font_google_target_css_path_src_pages_app_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_13___default().variable), \" \").concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: false,\n                disableTransitionOnChange: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: isRTL ? \"top-left\" : \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"var(--toast-bg)\",\n                                color: \"var(--toast-color)\",\n                                direction: isRTL ? \"rtl\" : \"ltr\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(App, \"vQduR7x+OPXj6PSmJyFnf+hU7bg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = App;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.appWithTranslation)(App));\nvar _c, _c1;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n"));

/***/ })

});